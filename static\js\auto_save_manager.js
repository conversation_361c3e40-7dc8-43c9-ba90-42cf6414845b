/**
 * 自动保存管理器
 * 负责监听表单变化并自动保存到后端
 */

class AutoSaveManager {
    constructor() {
        this.saveTimeout = null;
        this.saveDelay = 2000; // 2秒延迟保存
        this.isEnabled = true;
        this.lastSaveData = null;
        this.saveIndicator = null;
        this.currentConversationId = null;
        this.currentVersionId = null;
        
        this.initSaveIndicator();
        this.initFormListeners();
    }

    /**
     * 初始化保存状态指示器
     */
    initSaveIndicator() {
        // 创建保存状态指示器
        this.saveIndicator = document.createElement('div');
        this.saveIndicator.id = 'auto-save-indicator';
        this.saveIndicator.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            z-index: 10000;
            transition: all 0.3s ease;
            display: none;
        `;
        document.body.appendChild(this.saveIndicator);
    }

    /**
     * 显示保存状态
     */
    showSaveStatus(status, message) {
        if (!this.saveIndicator) return;

        const styles = {
            saving: {
                backgroundColor: '#fef3c7',
                color: '#92400e',
                border: '1px solid #fbbf24'
            },
            success: {
                backgroundColor: '#d1fae5',
                color: '#065f46',
                border: '1px solid #10b981'
            },
            error: {
                backgroundColor: '#fee2e2',
                color: '#991b1b',
                border: '1px solid #ef4444'
            }
        };

        const style = styles[status] || styles.saving;
        Object.assign(this.saveIndicator.style, style);
        this.saveIndicator.textContent = message;
        this.saveIndicator.style.display = 'block';

        // 自动隐藏成功和错误状态
        if (status === 'success' || status === 'error') {
            setTimeout(() => {
                if (this.saveIndicator) {
                    this.saveIndicator.style.display = 'none';
                }
            }, 3000);
        }
    }

    /**
     * 初始化表单监听器
     */
    initFormListeners() {
        // 监听所有表单输入
        document.addEventListener('input', (e) => {
            if (this.shouldAutoSave(e.target)) {
                this.scheduleAutoSave();
            }
        });

        // 监听选择框变化
        document.addEventListener('change', (e) => {
            if (this.shouldAutoSave(e.target)) {
                this.scheduleAutoSave();
            }
        });

        // 监听动态元素的添加和删除
        document.addEventListener('click', (e) => {
            if (e.target.matches('.add-member-btn, .remove-member-btn, .add-measure-btn, .remove-measure-btn')) {
                // 延迟保存，等待DOM更新
                setTimeout(() => {
                    this.scheduleAutoSave();
                }, 100);
            }
        });
    }

    /**
     * 判断是否应该自动保存
     */
    shouldAutoSave(element) {
        // 排除某些不需要保存的元素
        const excludeSelectors = [
            '#auto-save-indicator',
            '.search-input',
            '.filter-input',
            '[data-no-autosave]'
        ];

        for (const selector of excludeSelectors) {
            if (element.matches && element.matches(selector)) {
                return false;
            }
        }

        // 只保存表单相关的元素
        return element.matches('input, textarea, select') && 
               element.form && 
               this.isEnabled &&
               this.currentConversationId &&
               this.currentVersionId;
    }

    /**
     * 安排自动保存
     */
    scheduleAutoSave() {
        if (!this.isEnabled) return;

        // 清除之前的保存计划
        if (this.saveTimeout) {
            clearTimeout(this.saveTimeout);
        }

        // 安排新的保存
        this.saveTimeout = setTimeout(() => {
            this.performAutoSave();
        }, this.saveDelay);
    }

    /**
     * 执行自动保存
     */
    async performAutoSave() {
        if (!this.currentConversationId || !this.currentVersionId) {
            console.log('自动保存跳过：缺少对话ID或版本ID');
            return;
        }

        try {
            // 获取当前表单数据
            const formData = this.collectFormData();
            
            // 检查数据是否有变化
            const dataString = JSON.stringify(formData);
            if (dataString === this.lastSaveData) {
                console.log('自动保存跳过：数据无变化');
                return;
            }

            this.showSaveStatus('saving', '正在保存...');

            // 调用API保存数据
            const result = await window.apiClient.updateVersion(
                this.currentConversationId,
                this.currentVersionId,
                formData
            );

            if (result.success) {
                this.lastSaveData = dataString;
                this.showSaveStatus('success', '保存成功');
                console.log('自动保存成功');
                
                // 触发保存成功事件
                this.notifyAutoSaveSuccess(formData);
            } else {
                this.showSaveStatus('error', '保存失败');
                console.error('自动保存失败:', result.message);
            }

        } catch (error) {
            this.showSaveStatus('error', '保存出错');
            console.error('自动保存出错:', error);
        }
    }

    /**
     * 收集表单数据
     */
    collectFormData() {
        const formData = {};
        
        // 获取所有表单元素
        const form = document.querySelector('#d8-form');
        if (!form) return formData;

        const inputs = form.querySelectorAll('input, textarea, select');
        
        inputs.forEach(input => {
            if (input.name) {
                if (input.type === 'checkbox') {
                    formData[input.name] = input.checked;
                } else if (input.type === 'radio') {
                    if (input.checked) {
                        formData[input.name] = input.value;
                    }
                } else {
                    formData[input.name] = input.value;
                }
            }
        });

        return formData;
    }

    /**
     * 设置当前对话和版本
     */
    setCurrentContext(conversationId, versionId) {
        this.currentConversationId = conversationId;
        this.currentVersionId = versionId;
        this.lastSaveData = null; // 重置保存数据
        console.log(`自动保存上下文设置: 对话=${conversationId}, 版本=${versionId}`);
    }

    /**
     * 启用自动保存
     */
    enable() {
        this.isEnabled = true;
        console.log('自动保存已启用');
    }

    /**
     * 禁用自动保存
     */
    disable() {
        this.isEnabled = false;
        if (this.saveTimeout) {
            clearTimeout(this.saveTimeout);
            this.saveTimeout = null;
        }
        console.log('自动保存已禁用');
    }

    /**
     * 立即保存
     */
    async saveNow() {
        if (this.saveTimeout) {
            clearTimeout(this.saveTimeout);
            this.saveTimeout = null;
        }
        await this.performAutoSave();
    }

    /**
     * 通知自动保存成功
     */
    notifyAutoSaveSuccess(formData) {
        // 触发自定义事件
        const event = new CustomEvent('autoSaveSuccess', {
            detail: { formData, timestamp: new Date() }
        });
        document.dispatchEvent(event);
    }

    /**
     * 销毁自动保存管理器
     */
    destroy() {
        this.disable();
        if (this.saveIndicator) {
            this.saveIndicator.remove();
            this.saveIndicator = null;
        }
    }
}

// 创建全局实例
window.autoSaveManager = new AutoSaveManager();

// 监听自动保存成功事件
document.addEventListener('autoSaveSuccess', (e) => {
    console.log('自动保存成功事件:', e.detail);
});

console.log('自动保存管理器已初始化');
