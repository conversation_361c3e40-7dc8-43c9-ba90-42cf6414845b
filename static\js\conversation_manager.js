/**
 * 对话式8D报告管理系统
 * 支持多版本表单、对话历史和智能协作
 */

class ConversationManager {
    constructor() {
        this.currentConversation = null;
        this.activeVersion = null;
        this.conversations = new Map();
        this.listeners = new Map();

        this.initEventListeners();
        // 延迟加载，等待API客户端和用户认证完成
        this.initializeWithDelay();
    }

    /**
     * 延迟初始化，等待API客户端准备就绪
     */
    async initializeWithDelay() {
        // 等待API客户端和用户认证完成
        let retries = 0;
        const maxRetries = 10;

        while (retries < maxRetries) {
            if (window.apiClient) {
                try {
                    const user = await window.apiClient.getCurrentUser();
                    if (user) {
                        // 用户已登录，加载对话数据
                        await this.loadConversationsFromAPI();
                        console.log('对话管理器初始化完成');
                        return;
                    }
                } catch (error) {
                    // 用户未登录或认证失败，这是正常的
                }
            }

            // 等待500ms后重试
            await new Promise(resolve => setTimeout(resolve, 500));
            retries++;
        }

        console.log('对话管理器初始化超时，用户可能未登录');
    }

    /**
     * 初始化事件监听器
     */
    initEventListeners() {
        // 监听表单数据变化
        document.addEventListener('DOMContentLoaded', () => {
            this.bindFormEvents();
        });
    }

    /**
     * 生成唯一ID
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    /**
     * 生成对话标题
     */
    generateConversationTitle(formData = null) {
        // 如果有D0标题，优先使用
        if (formData && formData.d0_title && formData.d0_title.trim()) {
            return formData.d0_title.trim();
        }

        // 如果有问题描述，使用前20个字符
        if (formData && formData.d2_description && formData.d2_description.trim()) {
            const desc = formData.d2_description.trim().substring(0, 20);
            return `${desc}...`;
        }

        // 生成带序号的默认标题（不包含时间戳）
        // 计算当前已有的对话数量，用于生成序号
        const existingCount = this.conversations.size;
        const sequenceNumber = existingCount + 1;

        return `8D报告 #${sequenceNumber}`;
    }

    /**
     * 生成版本描述
     */
    generateVersionDescription(formData, source, customDescription = null, conversation = null) {
        if (customDescription) {
            return customDescription;
        }

        const timestamp = new Date().toLocaleString('zh-CN', {
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });

        if (source === 'user') {
            // 检查是否有实际内容
            const hasContent = this.hasFormContent(formData);
            if (!hasContent) {
                // 为空白版本生成编号，和新建对话一样
                const existingVersionCount = conversation ? conversation.form_versions.length : 0;
                const versionNumber = existingVersionCount + 1;
                return `空白版本 #${versionNumber}`;
            }

            // 如果有D0标题，使用它
            if (formData && formData.d0_title && formData.d0_title.trim()) {
                return `用户输入: ${formData.d0_title.trim().substring(0, 15)}...`;
            }

            // 如果有问题描述，使用它
            if (formData && formData.d2_description && formData.d2_description.trim()) {
                return `用户输入: ${formData.d2_description.trim().substring(0, 15)}...`;
            }

            return `用户输入`;
        } else if (source === 'ai') {
            return `AI优化版本`;
        } else {
            return `${source}版本`;
        }
    }

    /**
     * 检查表单是否有实际内容
     */
    hasFormContent(formData) {
        if (!formData || typeof formData !== 'object') {
            return false;
        }

        for (const key in formData) {
            const value = formData[key];
            if (value && typeof value === 'string' && value.trim() !== '') {
                return true;
            } else if (value && typeof value === 'object') {
                // 递归检查嵌套对象
                if (this.hasFormContent(value)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 创建新对话
     */
    createConversation(initialFormData = null, title = null) {
        const conversationId = this.generateId();
        const now = new Date().toISOString();
        
        const conversation = {
            conversation_id: conversationId,
            title: title || this.generateConversationTitle(initialFormData),
            created_at: now,
            updated_at: now,
            status: 'active',
            form_versions: [],
            chat_history: [],
            generated_documents: [],
            session_id: null // Dify会话ID
        };

        // 如果有初始数据，创建第一个版本
        if (initialFormData && !this.isFormEmpty(initialFormData)) {
            this.addFormVersion(conversation, initialFormData, 'user');
        }

        // 先将对话添加到内存中
        this.conversations.set(conversationId, conversation);
        this.currentConversation = conversation;

        // 保存到后端API
        this.saveConversationToAPI(conversation);
        this.notifyListeners('conversationCreated', conversation);

        // 设置自动保存上下文
        if (window.autoSaveManager) {
            window.autoSaveManager.setCurrentContext(conversationId, null);
        }

        return conversationId;
    }

    /**
     * 添加表单版本
     */
    addFormVersion(conversation, formData, createdBy = 'user', customName = null, modificationNotes = null) {
        // 生成唯一的版本ID，基于时间戳确保唯一性
        const timestamp = Date.now();
        const versionId = `v${timestamp}`;
        const now = new Date().toISOString();

        // 使用新的版本描述生成逻辑，传递conversation参数用于生成编号
        const versionName = customName || this.generateVersionDescription(formData, createdBy, null, conversation);

        // 设置所有版本为非活跃
        conversation.form_versions.forEach(v => v.is_active = false);

        const version = {
            version_id: versionId,
            version_name: versionName,
            created_at: now,
            created_by: createdBy,
            form_data: JSON.parse(JSON.stringify(formData)), // 深拷贝
            modification_notes: modificationNotes || null, // 添加修改意见字段
            is_active: true
        };

        // 将新版本添加到数组开头，使新版本显示在最上方
        conversation.form_versions.unshift(version);
        conversation.updated_at = now;
        this.activeVersion = versionId;

        // 保存版本到后端API
        this.saveVersionToAPI(conversation.id, version);
        this.notifyListeners('versionAdded', { conversation, version });

        // 设置自动保存上下文
        if (window.autoSaveManager) {
            window.autoSaveManager.setCurrentContext(conversation.id, versionId);
        }

        return versionId;
    }

    /**
     * 切换到指定版本
     */
    switchToVersion(versionId) {
        if (!this.currentConversation) return false;

        const version = this.currentConversation.form_versions.find(v => v.version_id === versionId);
        if (!version) return false;

        // 设置所有版本为非活跃
        this.currentConversation.form_versions.forEach(v => v.is_active = false);
        version.is_active = true;
        this.activeVersion = versionId;

        // 数据持久化由后端API管理，无需本地存储
        this.notifyListeners('versionSwitched', { conversation: this.currentConversation, version });

        // 设置自动保存上下文
        if (window.autoSaveManager) {
            window.autoSaveManager.setCurrentContext(this.currentConversation.id, versionId);
        }

        return true;
    }

    /**
     * 获取当前活跃版本
     */
    getActiveVersion() {
        if (!this.currentConversation) return null;
        return this.currentConversation.form_versions.find(v => v.is_active);
    }

    /**
     * 删除版本
     */
    deleteVersion(versionId) {
        if (!this.currentConversation) return false;

        const versionIndex = this.currentConversation.form_versions.findIndex(v => v.version_id === versionId);
        if (versionIndex === -1) return false;

        // 不能删除唯一的版本
        if (this.currentConversation.form_versions.length <= 1) {
            return false;
        }

        const deletedVersion = this.currentConversation.form_versions[versionIndex];
        const wasActive = deletedVersion.is_active;

        // 删除版本
        this.currentConversation.form_versions.splice(versionIndex, 1);

        // 如果删除的是活跃版本，需要激活另一个版本
        if (wasActive && this.currentConversation.form_versions.length > 0) {
            // 激活最后一个版本
            const lastVersion = this.currentConversation.form_versions[this.currentConversation.form_versions.length - 1];
            lastVersion.is_active = true;
            this.activeVersion = lastVersion.version_id;
        }

        this.currentConversation.updated_at = new Date().toISOString();
        // 数据持久化由后端API管理，无需本地存储
        this.notifyListeners('versionDeleted', { 
            conversation: this.currentConversation, 
            deletedVersionId: versionId,
            newActiveVersion: wasActive ? this.getActiveVersion() : null
        });
        
        // 删除版本成功
        return true;
    }

    /**
     * 添加聊天消息
     */
    addChatMessage(sender, message, versionBefore = null, versionAfter = null) {
        if (!this.currentConversation) return null;

        const messageId = this.generateId();
        const chatMessage = {
            message_id: messageId,
            sender: sender, // 'user' 或 'ai'
            message: message,
            timestamp: new Date().toISOString(),
            version_before: versionBefore,
            version_after: versionAfter
        };

        this.currentConversation.chat_history.push(chatMessage);
        this.currentConversation.updated_at = new Date().toISOString();
        
        // 数据持久化由后端API管理，无需本地存储
        this.notifyListeners('messageAdded', { conversation: this.currentConversation, message: chatMessage });
        
        return messageId;
    }

    /**
     * 加载对话
     */
    loadConversation(conversationId) {
        const conversation = this.conversations.get(conversationId);
        if (!conversation) return false;

        this.currentConversation = conversation;
        
        // 找到活跃版本
        const activeVersion = conversation.form_versions.find(v => v.is_active);
        if (activeVersion) {
            this.activeVersion = activeVersion.version_id;
        } else if (conversation.form_versions.length > 0) {
            // 如果没有活跃版本，激活最后一个版本
            const lastVersion = conversation.form_versions[conversation.form_versions.length - 1];
            lastVersion.is_active = true;
            this.activeVersion = lastVersion.version_id;
            // 数据持久化由后端API管理，无需本地存储
        }

        this.notifyListeners('conversationLoaded', conversation);

        // 设置自动保存上下文
        if (window.autoSaveManager && this.activeVersion) {
            window.autoSaveManager.setCurrentContext(conversationId, this.activeVersion);
        }

        return true;
    }

    /**
     * 删除对话
     */
    deleteConversation(conversationId) {
        const conversation = this.conversations.get(conversationId);
        if (!conversation) return false;

        this.conversations.delete(conversationId);
        
        if (this.currentConversation && this.currentConversation.conversation_id === conversationId) {
            this.currentConversation = null;
            this.activeVersion = null;
        }

        // 数据持久化由后端API管理，无需本地存储
        this.notifyListeners('conversationDeleted', conversationId);
        return true;
    }

    /**
     * 获取所有对话列表
     */
    getAllConversations() {
        return Array.from(this.conversations.values()).sort((a, b) => 
            new Date(b.updated_at) - new Date(a.updated_at)
        );
    }

    /**
     * 检查表单是否为空
     */
    isFormEmpty(formData) {
        if (!formData || typeof formData !== 'object') return true;
        
        for (const key in formData) {
            if (formData[key] && String(formData[key]).trim() !== '') {
                return false;
            }
        }
        return true;
    }

    /**
     * 从后端API加载对话数据
     */
    async loadConversationsFromAPI() {
        try {
            if (window.apiClient) {
                const conversations = await window.apiClient.getConversations();
                this.conversations.clear();

                conversations.forEach(conv => {
                    // 转换为内部格式
                    const conversation = {
                        id: conv.conversation_id,
                        title: conv.title,
                        description: conv.description,
                        created_at: conv.created_at,
                        updated_at: conv.updated_at,
                        form_versions: [],
                        chat_history: []
                    };
                    this.conversations.set(conv.conversation_id, conversation);
                });

                console.log(`从API加载了 ${conversations.length} 个对话`);

                // 通知监听器对话列表已更新
                this.notifyListeners('conversationsLoaded', Array.from(this.conversations.values()));
            }
        } catch (error) {
            console.error('从API加载对话失败:', error);
            this.conversations = new Map();
        }
    }

    /**
     * 重新加载对话数据（公共方法）
     */
    async reloadConversations() {
        await this.loadConversationsFromAPI();
    }

    /**
     * 保存单个对话到后端API
     */
    async saveConversationToAPI(conversation) {
        try {
            if (window.apiClient) {
                const result = await window.apiClient.createConversation(
                    conversation.id,
                    conversation.title,
                    conversation.description
                );
                return result.success;
            }
            return false;
        } catch (error) {
            console.error('保存对话到API失败:', error);
            return false;
        }
    }

    /**
     * 保存版本到后端API
     */
    async saveVersionToAPI(conversationId, version) {
        try {
            if (window.apiClient) {
                const result = await window.apiClient.addVersion(
                    conversationId,
                    version.version_id,
                    version.version_name,
                    version.form_data,
                    version.created_by,
                    version.modification_notes
                );
                return result.success;
            }
            return false;
        } catch (error) {
            console.error('保存版本到API失败:', error);
            return false;
        }
    }

    /**
     * 保存版本到后端API
     */
    async saveVersionToAPI(conversationId, version) {
        try {
            if (window.apiClient) {
                const result = await window.apiClient.createVersion(
                    conversationId,
                    version.version_id,
                    version.version_name,
                    version.form_data,
                    version.created_by
                );
                if (result.success) {
                    console.log('版本保存成功:', version.version_id);
                } else {
                    console.error('版本保存失败:', result.message);
                }
                return result.success;
            }
            return false;
        } catch (error) {
            console.error('保存版本到API失败:', error);
            return false;
        }
    }

    /**
     * 清理过期对话（现在由后端管理，此方法保留为空）
     */
    cleanupExpiredConversations(daysToKeep = 30) {
        // 数据现在存储在后端，清理工作由后端管理
        console.log('清理过期对话现在由后端管理');
    }

    /**
     * 添加事件监听器
     */
    addEventListener(event, callback) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event).push(callback);
    }

    /**
     * 移除事件监听器
     */
    removeEventListener(event, callback) {
        if (!this.listeners.has(event)) return;
        const callbacks = this.listeners.get(event);
        const index = callbacks.indexOf(callback);
        if (index > -1) {
            callbacks.splice(index, 1);
        }
    }

    /**
     * 通知监听器
     */
    notifyListeners(event, data) {
        if (!this.listeners.has(event)) return;
        this.listeners.get(event).forEach(callback => {
            try {
                callback(data);
            } catch (error) {
                // 静默处理错误
            }
        });
    }

    /**
     * 绑定表单事件
     */
    bindFormEvents() {
        // 这里将在后续实现中绑定表单变化事件

    }



    /**
     * 导入对话数据
     */
    importConversation(conversationData) {
        try {
            // 生成新的ID以避免冲突
            const newId = this.generateId();
            const conversation = {
                ...conversationData,
                conversation_id: newId,
                imported_at: new Date().toISOString()
            };

            this.conversations.set(newId, conversation);
            // 数据持久化由后端API管理，无需本地存储
            
            return newId;
        } catch (error) {
            return null;
        }
    }
}

// 全局对话管理器实例
window.conversationManager = new ConversationManager();

// 暴露给其他脚本使用
window.ConversationManager = ConversationManager; 